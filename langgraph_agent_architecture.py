from typing import TypedDict, List, Dict, Any, Annotated
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor, ToolInvocation
from langchain_core.messages import HumanMessage, AIMessage
from langchain_openai import ChatOpenAI
import asyncio

# 定义状态结构
class StudyAbroadAgentState(TypedDict):
    messages: Annotated[List[Dict], "聊天消息历史"]
    user_profile: Annotated[Dict, "用户基本信息"]
    academic_info: Annotated[Dict, "学术背景信息"]
    transcript_data: Annotated[Dict, "成绩单数据"]
    preferences: Annotated[Dict, "申请偏好"]
    matched_universities: Annotated[List[Dict], "匹配的大学列表"]
    selected_schools: Annotated[List[Dict], "用户选择的学校"]
    resume_data: Annotated[Dict, "简历信息"]
    generated_essays: Annotated[Dict, "生成的文书"]
    current_step: Annotated[str, "当前步骤"]
    needs_clarification: Annotated[bool, "是否需要澄清"]
    error_message: Annotated[str, "错误信息"]

# 核心Agent类
class StudyAbroadAgent:
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-4-turbo", temperature=0.7)
        self.tools = self._initialize_tools()
        self.graph = self._create_graph()
    
    def _initialize_tools(self):
        """初始化工具集"""
        return {
            'ocr_processor': OCRProcessor(),
            'gpa_calculator': GPACalculator(),
            'university_matcher': UniversityMatcher(),
            'essay_generator': EssayGenerator(),
            'data_validator': DataValidator()
        }
    
    def _create_graph(self):
        """创建工作流图"""
        workflow = StateGraph(StudyAbroadAgentState)
        
        # 添加节点
        workflow.add_node("router", self._route_conversation)
        workflow.add_node("collect_basic_info", self._collect_basic_info)
        workflow.add_node("process_transcript", self._process_transcript)
        workflow.add_node("collect_preferences", self._collect_preferences)
        workflow.add_node("match_universities", self._match_universities)
        workflow.add_node("school_selection", self._handle_school_selection)
        workflow.add_node("process_resume", self._process_resume)
        workflow.add_node("generate_essays", self._generate_essays)
        workflow.add_node("clarification", self._handle_clarification)
        workflow.add_node("error_handler", self._handle_errors)
        
        # 设置入口点
        workflow.set_entry_point("router")
        
        # 定义路由逻辑
        workflow.add_conditional_edges(
            "router",
            self._determine_next_step,
            {
                "collect_basic_info": "collect_basic_info",
                "process_transcript": "process_transcript", 
                "collect_preferences": "collect_preferences",
                "match_universities": "match_universities",
                "school_selection": "school_selection",
                "process_resume": "process_resume",
                "generate_essays": "generate_essays",
                "clarification": "clarification",
                "error": "error_handler",
                "end": END
            }
        )
        
        # 设置节点间的边
        workflow.add_edge("collect_basic_info", "router")
        workflow.add_edge("process_transcript", "router")
        workflow.add_edge("collect_preferences", "router")
        workflow.add_edge("match_universities", "router")
        workflow.add_edge("school_selection", "router")
        workflow.add_edge("process_resume", "router")
        workflow.add_edge("generate_essays", END)
        workflow.add_edge("clarification", "router")
        workflow.add_edge("error_handler", "router")
        
        return workflow.compile()
    
    def _route_conversation(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """对话路由器 - 决定下一步动作"""
        current_step = state.get("current_step", "start")
        
        # 分析最新消息内容
        if state["messages"]:
            latest_message = state["messages"][-1]["content"]
            
            # 使用LLM判断用户意图
            intent_prompt = f"""
            分析用户消息，判断用户当前的意图和所处阶段：
            
            用户消息: {latest_message}
            当前步骤: {current_step}
            用户档案完整度: {self._check_profile_completeness(state)}
            
            返回下一步应该执行的动作，选择之一：
            - collect_basic_info: 收集基本信息
            - process_transcript: 处理成绩单
            - collect_preferences: 收集申请偏好
            - match_universities: 匹配大学
            - school_selection: 处理学校选择
            - process_resume: 处理简历
            - generate_essays: 生成申请文书
            - clarification: 需要澄清信息
            - end: 对话结束
            """
            
            response = self.llm.invoke(intent_prompt)
            next_action = response.content.strip()
            
        else:
            next_action = "collect_basic_info"
        
        state["current_step"] = next_action
        return state
    
    def _determine_next_step(self, state: StudyAbroadAgentState) -> str:
        """确定下一步动作"""
        if state.get("needs_clarification"):
            return "clarification"
        if state.get("error_message"):
            return "error"
        
        return state.get("current_step", "collect_basic_info")
    
    def _collect_basic_info(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """收集用户基本信息"""
        if not state.get("user_profile"):
            response = """
            👋 欢迎使用留学申请智能助手！我会帮助您找到最适合的研究生项目。
            
            让我们先了解一下您的基本情况：
            1. 您的本科院校名称？
            2. 本科专业是什么？
            3. 您的GPA是多少？（如果不确定，稍后可以上传成绩单让我帮您计算）
            
            请告诉我这些信息，或者您可以直接上传成绩单图片。
            """
        else:
            # 验证信息完整性
            missing_fields = []
            profile = state["user_profile"]
            
            if not profile.get("university"):
                missing_fields.append("本科院校")
            if not profile.get("major"):
                missing_fields.append("本科专业")
            if not profile.get("gpa") and not state.get("transcript_data"):
                missing_fields.append("GPA或成绩单")
            
            if missing_fields:
                response = f"还需要补充以下信息：{', '.join(missing_fields)}"
                state["needs_clarification"] = True
            else:
                response = "✅ 基本信息收集完成！接下来处理您的学术成绩。"
                state["current_step"] = "process_transcript"
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _process_transcript(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """处理成绩单"""
        if state.get("transcript_data") and state["transcript_data"].get("processed"):
            # 成绩单已处理，继续下一步
            state["current_step"] = "collect_preferences"
            response = "✅ 成绩单处理完成！现在让我了解您的申请偏好。"
        
        elif not state.get("transcript_data"):
            # 需要上传成绩单或手动输入GPA
            if state["user_profile"].get("gpa"):
                # 已有GPA，跳过成绩单处理
                state["current_step"] = "collect_preferences"
                response = "✅ 使用您提供的GPA信息。现在让我了解您的申请偏好。"
            else:
                response = """
                📋 请上传您的成绩单图片，我会帮您：
                1. 识别各科成绩
                2. 计算准确的GPA
                3. 分析您的学术优势
                
                上传图片后，如果识别有误，您可以手动修正。
                """
        else:
            # 处理上传的成绩单
            try:
                ocr_result = self.tools['ocr_processor'].process(state["transcript_data"])
                gpa = self.tools['gpa_calculator'].calculate(ocr_result["courses"])
                
                state["transcript_data"]["processed"] = True
                state["transcript_data"]["courses"] = ocr_result["courses"]
                state["user_profile"]["gpa"] = gpa
                
                response = f"""
                ✅ 成绩单处理完成！
                
                📊 识别结果：
                - 总GPA: {gpa}
                - 课程数量: {len(ocr_result['courses'])}
                
                如果信息有误，请告诉我需要修正的地方。否则我们继续下一步。
                """
                state["current_step"] = "collect_preferences"
                
            except Exception as e:
                state["error_message"] = f"成绩单处理失败: {str(e)}"
                response = "❌ 成绩单处理出现问题，请重新上传或手动输入GPA。"
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _collect_preferences(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """收集申请偏好"""
        if not state.get("preferences"):
            response = """
            🎯 请告诉我您的申请偏好：
            
            1. **目标地区**（可多选）：
               - 🇺🇸 美国
               - 🇨🇦 加拿大  
               - 🇬🇧 英国
               - 🇦🇺 澳大利亚
               - 🇸🇬 新加坡
               - 🇯🇵 日本
               - 🇰🇷 韩国
               - 🇪🇺 欧洲其他国家
            
            2. **目标专业**：
               - 计算机科学 (CS)
               - 人工智能 (AI)
               - 数据科学
               - 商科 (MBA/其他)
               - 工程类
               - 其他（请具体说明）
            
            3. **其他偏好**：
               - 学费预算范围
               - 学制偏好（1年/2年）
               - 排名要求
            """
        else:
            # 验证偏好信息完整性
            prefs = state["preferences"]
            if prefs.get("regions") and prefs.get("target_major"):
                response = "✅ 申请偏好收集完成！正在为您匹配合适的大学..."
                state["current_step"] = "match_universities"
            else:
                response = "请补充完整的地区和专业偏好信息。"
                state["needs_clarification"] = True
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _match_universities(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """匹配大学"""
        try:
            # 调用匹配算法
            matches = self.tools['university_matcher'].find_matches(
                user_profile=state["user_profile"],
                preferences=state["preferences"]
            )
            
            state["matched_universities"] = matches
            
            # 生成推荐结果展示
            response = self._format_university_matches(matches)
            response += "\n\n请从以上20所大学中选择您感兴趣的学校编号（最多10个），例如：1,3,5,7"
            
            state["current_step"] = "school_selection"
            
        except Exception as e:
            state["error_message"] = f"大学匹配失败: {str(e)}"
            response = "❌ 匹配过程出现问题，请稍后重试。"
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _handle_school_selection(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """处理学校选择"""
        latest_message = state["messages"][-1]["content"]
        
        try:
            # 解析用户选择的学校编号
            selected_indices = self._parse_school_selection(latest_message)
            
            if selected_indices:
                selected_schools = [
                    state["matched_universities"][i-1] 
                    for i in selected_indices 
                    if 0 < i <= len(state["matched_universities"])
                ]
                
                state["selected_schools"] = selected_schools
                
                response = f"""
                ✅ 您选择了 {len(selected_schools)} 所大学！
                
                现在请上传您的简历，我会为每所学校生成个性化的申请文书。
                
                📄 简历应包含：
                - 教育背景
                - 实习/工作经历  
                - 项目经验
                - 技能特长
                - 获奖情况
                """
                
                state["current_step"] = "process_resume"
            else:
                response = "请提供有效的学校编号，格式如：1,3,5,7"
                state["needs_clarification"] = True
                
        except Exception as e:
            state["error_message"] = f"选择处理失败: {str(e)}"
            response = "❌ 处理您的选择时出现问题，请重新选择。"
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _process_resume(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """处理简历"""
        if state.get("resume_data"):
            response = "✅ 简历处理完成！正在生成个性化申请文书..."
            state["current_step"] = "generate_essays"
        else:
            response = """
            📄 请上传您的简历文件（PDF/Word格式）或直接粘贴简历内容。
            
            简历将用于生成个性化的申请文书，包括：
            - Statement of Purpose
            - Personal Statement  
            - 推荐信要点
            """
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _generate_essays(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """生成申请文书"""
        try:
            essays = {}
            
            for school in state["selected_schools"]:
                essay = self.tools['essay_generator'].generate(
                    user_profile=state["user_profile"],
                    resume_data=state["resume_data"],
                    target_school=school
                )
                essays[school["id"]] = essay
            
            state["generated_essays"] = essays
            
            response = f"""
            🎉 申请文书生成完成！
            
            为您的 {len(state['selected_schools'])} 所目标学校生成了个性化文书：
            
            📝 包含内容：
            - Statement of Purpose (每校800-1000字)
            - 申请要点总结
            - 个性化亮点突出
            
            您可以下载查看，如需修改请告诉我具体要求。
            
            🎯 祝您申请顺利！如还有其他问题，随时联系。
            """
            
        except Exception as e:
            state["error_message"] = f"文书生成失败: {str(e)}"
            response = "❌ 文书生成出现问题，请稍后重试。"
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _handle_clarification(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """处理澄清请求"""
        # 重置澄清标志
        state["needs_clarification"] = False
        
        # 根据当前步骤提供针对性帮助
        current_step = state.get("current_step", "")
        
        clarification_responses = {
            "collect_basic_info": "请提供您的本科院校、专业和GPA信息。",
            "process_transcript": "请上传清晰的成绩单图片，或手动输入GPA。",
            "collect_preferences": "请选择目标地区和专业偏好。",
            "school_selection": "请从推荐列表中选择学校编号，如：1,3,5,7"
        }
        
        response = clarification_responses.get(current_step, "请提供更多信息以便我更好地帮助您。")
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    def _handle_errors(self, state: StudyAbroadAgentState) -> StudyAbroadAgentState:
        """处理错误"""
        error_msg = state.get("error_message", "")
        
        response = f"""
        ⚠️ 遇到一些问题：{error_msg}
        
        请尝试：
        1. 重新上传文件
        2. 检查信息格式
        3. 或者手动输入相关信息
        
        我会继续为您提供帮助！
        """
        
        # 清除错误状态
        state["error_message"] = ""
        
        state["messages"].append({"role": "assistant", "content": response})
        return state
    
    # 辅助方法
    def _check_profile_completeness(self, state: StudyAbroadAgentState) -> str:
        """检查用户档案完整度"""
        profile = state.get("user_profile", {})
        required_fields = ["university", "major", "gpa"]
        
        completed = sum(1 for field in required_fields if profile.get(field))
        return f"{completed}/{len(required_fields)}"
    
    def _format_university_matches(self, matches: List[Dict]) -> str:
        """格式化大学匹配结果"""
        response = "🎓 为您推荐以下20所大学：\n\n"
        
        for i, match in enumerate(matches, 1):
            response += f"""
**{i}. {match['university_name']}**
- 专业：{match['program_name']}
- 学制：{match['duration']}
- 学费：{match['tuition_fee']}
- QS排名：{match['qs_ranking']}
- 语言要求：{match['language_requirements']}
- 申请截止：{match['deadline']}
- 官网：{match['website']}

"""
        
        return response
    
    def _parse_school_selection(self, message: str) -> List[int]:
        """解析学校选择"""
        import re
        
        # 提取数字
        numbers = re.findall(r'\d+', message)
        return [int(num) for num in numbers if 1 <= int(num) <= 20]
    
    async def chat(self, message: str, state: StudyAbroadAgentState = None) -> Dict:
        """主要聊天接口"""
        if state is None:
            state = {
                "messages": [],
                "user_profile": {},
                "academic_info": {},
                "transcript_data": {},
                "preferences": {},
                "matched_universities": [],
                "selected_schools": [],
                "resume_data": {},
                "generated_essays": {},
                "current_step": "start",
                "needs_clarification": False,
                "error_message": ""
            }
        
        # 添加用户消息
        state["messages"].append({"role": "user", "content": message})
        
        # 执行工作流
        result = await self.graph.ainvoke(state)
        
        return {
            "response": result["messages"][-1]["content"],
            "state": result,
            "current_step": result.get("current_step"),
            "completed": result.get("current_step") == "generate_essays"
        }

# 使用示例
async def main():
    agent = StudyAbroadAgent()
    
    # 模拟对话流程
    state = None
    
    # 第一轮对话
    result1 = await agent.chat("你好，我想申请研究生", state)
    print("助手:", result1["response"])
    state = result1["state"]
    
    # 第二轮对话
    result2 = await agent.chat("我本科是清华大学计算机系，GPA 3.8", state)
    print("助手:", result2["response"])
    state = result2["state"]
    
    # 继续对话...

if __name__ == "__main__":
    asyncio.run(main())